import os
import gradio as gr
import base64
from zhipuai import ZhipuAI
from concurrent.futures import ThreadPoolExecutor
import threading
import pandas as pd

# 图片文字识别功能
DEFAULT_API_KEY = "a95934598bc145328b2a1170e251242e.w5JWPbRPrER3cLYW"
DEFAULT_PROMPT = """# # 角色
你是一位专业的中草药识别专家，具备深厚的中草药知识底蕴，能够精准识别各种中草药。当用户提供一张图片时，你可以从给定列表中筛选出最符合图片特征的中药，并给出详细的识别依据和该中药的相关知识。

## 技能
### 技能 1: 识别中草药
1. 用户提供一张中草药图片后，仔细观察图片细节，从给定列表里筛选出最符合条件的中药。
2. 详细阐述识别的依据，包括中药的外观特征（如形状、颜色、纹理等）与图片的匹配度。
3. 提供该中药的一些常见信息，例如其性味归经、功效主治、生长环境等。

## 限制:
- 仅围绕中草药识别相关内容进行交流，不回答与中草药识别无关的问题。
- 输出内容要有条理，识别依据和中药相关信息需清晰阐述。 

## 中草药列表：
"""

def extract_text_from_image(img_path, api_key, model_name, prompt):
    with open(img_path, 'rb') as img_file:
        img_base = base64.b64encode(img_file.read()).decode('utf-8')

    client = ZhipuAI(api_key=api_key)
    response = client.chat.completions.create(
        model=model_name,
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{img_base}"
                        }
                    },
                    {
                        "type": "text",
                        "text": prompt
                    }
                ]
            }
        ]
    )
    return os.path.basename(img_path), response.choices[0].message.content

def process_images(files_or_folder, api_key, model_name, prompt):
    image_paths = []
    if isinstance(files_or_folder, list):  # 处理批量上传的文件
        for file in files_or_folder:
            if os.path.isfile(file.name) and file.name.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_paths.append(file.name)
    elif isinstance(files_or_folder, str):  # 处理文件夹路径
        for root, _, files in os.walk(files_or_folder):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    image_paths.append(os.path.join(root, file))
    
    results = []
    with ThreadPoolExecutor(max_workers=5) as executor:
        # 提交任务到线程池
        futures = [executor.submit(extract_text_from_image, path, api_key, model_name, prompt) for path in image_paths]
        for future in futures:
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                results.append((os.path.basename(image_paths[futures.index(future)]), f"处理图片时出错: {str(e)}"))
    
    return results

def save_to_excel(results, output_path="识别结果.xlsx"):
    df = pd.DataFrame(results, columns=["图片名", "识别结果"])
    df.to_excel(output_path, index=False)
    return output_path

# Gradio 界面
with gr.Blocks() as ocr_ui:
    gr.Markdown("# 智谱大模型图片文字提取")
    with gr.Group():
        api_key_input = gr.Textbox(label="API Key", placeholder="请输入您的 API Key", type="password", value=DEFAULT_API_KEY)
        file_input = gr.File(file_types=["image"], label="批量上传图片", file_count="multiple")
        folder_input = gr.File(label="上传文件夹", file_count="directory")
        model_selector = gr.Dropdown(
            choices=["GLM-4.1V-Thinking-Flash", "GLM-4V-Flash", "glm-4v-plus-0111"],
            value="GLM-4.1V-Thinking-Flash",
            label="选择模型"
        )
        prompt_input = gr.Textbox(label="自定义提示词", value=DEFAULT_PROMPT, lines=10)
        text_output = gr.Textbox(label="识别结果", lines=10, interactive=False)
        output_file = gr.File(label="下载识别结果 Excel", interactive=False)
        submit_button = gr.Button("提交", variant="primary")

    def combined_inputs(file_input, folder_input, api_key, model_name, prompt):
        if file_input:
            results = process_images(file_input, api_key, model_name, prompt)
        elif folder_input:
            # 获取文件夹路径
            folder_path = os.path.dirname(folder_input[0].name)
            results = process_images(folder_path, api_key, model_name, prompt)
        else:
            results = []

        text_result = "\n\n".join([f"图片 {name} 的识别结果:\n{result}" for name, result in results])
        excel_path = save_to_excel(results)
        return text_result, excel_path

    submit_button.click(
        fn=combined_inputs,
        inputs=[file_input, folder_input, api_key_input, model_selector, prompt_input],
        outputs=[text_output, output_file]
    )

if __name__ == "__main__":
    ocr_ui.launch()